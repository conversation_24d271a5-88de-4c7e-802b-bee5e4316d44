﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" DefaultTargets="Build">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{3738ab56-3776-4bfa-b9dc-9d47412a5003}</ProjectGuid>
    <ProjectHome />
    <StartupFile>GOOD ONE - direct_tensorrt_fp16.py</StartupFile>
    <SearchPath />
    <WorkingDirectory>.\</WorkingDirectory>
    <OutputPath>.</OutputPath>
    <ProjectTypeGuids>{888888a0-9f3d-457c-b088-3a5042f75d52}</ProjectTypeGuids>
    <LaunchProvider>Standard Python launcher</LaunchProvider>
    <InterpreterId>CondaEnv|CondaEnv|tensorrt_env</InterpreterId>
    <SuppressConfigureTestFrameworkPrompt>true</SuppressConfigureTestFrameworkPrompt>
    <IsWindowsApplication>False</IsWindowsApplication>
    <CommandLineArguments>
    </CommandLineArguments>
    <EnableNativeCodeDebugging>False</EnableNativeCodeDebugging>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'" />
  <PropertyGroup Condition="'$(Configuration)' == 'Release'" />
  <PropertyGroup>
    <VisualStudioVersion Condition=" '$(VisualStudioVersion)' == '' ">10.0</VisualStudioVersion>
  </PropertyGroup>
  <ItemGroup>
    <Content Include="configs\extra_dataset\InSPyReNet_SwinB_DH.yaml" />
    <Content Include="configs\extra_dataset\InSPyReNet_SwinB_DHU_LR.yaml" />
    <Content Include="configs\extra_dataset\InSPyReNet_SwinB_DH_LR.yaml" />
    <Content Include="configs\extra_dataset\InSPyReNet_SwinB_DIS5K.yaml" />
    <Content Include="configs\extra_dataset\InSPyReNet_SwinB_DIS5K_LR.yaml" />
    <Content Include="configs\extra_dataset\InSPyReNet_SwinB_HU.yaml" />
    <Content Include="configs\extra_dataset\InSPyReNet_SwinB_HU_LR.yaml" />
    <Content Include="configs\extra_dataset\Plus_Ultra.yaml" />
    <Content Include="configs\extra_dataset\Plus_Ultra_LR.yaml" />
    <Content Include="configs\InSPyReNet_Res2Net50.yaml" />
    <Content Include="configs\InSPyReNet_SwinB.yaml" />
    <Content Include="environment.yml" />
    <Content Include="figures\demo_image.gif" />
    <Content Include="figures\demo_type.png" />
    <Content Include="figures\demo_video.gif" />
    <Content Include="figures\demo_webapp.gif" />
    <Content Include="figures\fig_architecture.png" />
    <Content Include="figures\fig_pyramid_blending.png" />
    <Content Include="figures\fig_qualitative.png" />
    <Content Include="figures\fig_qualitative2.png" />
    <Content Include="figures\fig_qualitative3.jpg" />
    <Content Include="figures\fig_qualitative_dis.png" />
    <Content Include="figures\fig_quantitative.png" />
    <Content Include="figures\fig_quantitative2.png" />
    <Content Include="figures\fig_quantitative3.png" />
    <Content Include="figures\fig_quantitative4.png" />
    <Content Include="requirements.txt" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="convert_onnx_to_tensorrt.py" />
    <Compile Include="convert_to_tensorrt with freezing.py" />
    <Compile Include="convert_to_tensorrt Directly.py" />
    <Compile Include="convert_to_tensorrt with Torchscript.py" />
    <Compile Include="convert_to_tensorrt with onnx.py" />
    <Compile Include="data\custom_transforms.py" />
    <Compile Include="data\dataloader.py" />
    <Compile Include="direct_fp16_fix.py" />
    <Compile Include="fix_1280x720_fp16_nan.py" />
    <Compile Include="GOOD ONE - direct_tensorrt_fp16.py" />
    <Compile Include="Expr.py" />
    <Compile Include="fix_fp16_type_mismatch.py" />
    <Compile Include="lib\backbones\Res2Net_v1b.py" />
    <Compile Include="lib\backbones\SwinTransformer.py" />
    <Compile Include="lib\InSPyReNet.py" />
    <Compile Include="lib\modules\attention_module.py" />
    <Compile Include="lib\modules\context_module.py" />
    <Compile Include="lib\modules\decoder_module.py" />
    <Compile Include="lib\modules\layers.py" />
    <Compile Include="lib\optim\losses.py" />
    <Compile Include="lib\optim\scheduler.py" />
    <Compile Include="lib\optim\__init__.py" />
    <Compile Include="lib\__init__.py" />
    <Compile Include="manual_fp16_conversion.py" />
    <Compile Include="onnx_convert plus ultra models to fp16 buggy.py" />
    <Compile Include="onnx_create plus ultra LR.py" />
    <Compile Include="GOOD ONE TO USE - onnx_create plus ultra.py" />
    <Compile Include="onnx_optimize for cuda multibatches.py" />
    <Compile Include="onnx_optimize for dml.py" />
    <Compile Include="onnx_optimize plus ultra for cuda.py" />
    <Compile Include="onnx_test_with_dml.py" />
    <Compile Include="quick_fp16_fix.py" />
    <Compile Include="run\Eval.py" />
    <Compile Include="run\Inference with Tensor Cores and Compile.py" />
    <Compile Include="run\Inference with Tensor Cores.py" />
    <Compile Include="run\Inference.py" />
    <Compile Include="InferenceTensorRT.py" />
    <Compile Include="run\Test.py" />
    <Compile Include="run\Train.py" />
    <Compile Include="run\__init__.py" />
    <Compile Include="run_fp16_test_fixed.py" />
    <Compile Include="tensorrt_diagnostic.py" />
    <Compile Include="test_corrected_fp16_models.py" />
    <Compile Include="test_model_output.py" />
    <Compile Include="test_tensorrt_fp16_fixed.py" />
    <Compile Include="test_tensorrt_fp16_simple.py" />
    <Compile Include="use_onnx - tensorrt.py" />
    <Compile Include="use_onnx - cuda - fp16.py" />
    <Compile Include="use_onnx - cuda.py" />
    <Compile Include="use_onnx - tensorrt batched.py" />
    <Compile Include="use_onnx - cuda batched.py" />
    <Compile Include="use_onnx.py" />
    <Compile Include="use_onnx_cuda_fp32.py" />
    <Compile Include="utils\benchmark.py" />
    <Compile Include="utils\download.py" />
    <Compile Include="utils\eval_functions.py" />
    <Compile Include="utils\misc.py" />
    <Compile Include="utils\__init__.py" />
    <Compile Include="verify_tensorrt_fp16_success.py" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="configs\" />
    <Folder Include="configs\extra_dataset\" />
    <Folder Include="data" />
    <Folder Include="figures" />
    <Folder Include="lib" />
    <Folder Include="lib\backbones" />
    <Folder Include="lib\modules" />
    <Folder Include="lib\optim" />
    <Folder Include="run" />
    <Folder Include="utils" />
  </ItemGroup>
  <ItemGroup>
    <InterpreterReference Include="CondaEnv|CondaEnv|inspyrenet" />
    <InterpreterReference Include="CondaEnv|CondaEnv|onnxgpu" />
    <InterpreterReference Include="CondaEnv|CondaEnv|tensorrt_env" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Python Tools\Microsoft.PythonTools.targets" />
</Project>