{"Version": 1, "WorkspaceRootPath": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\GOOD ONE - direct_tensorrt_fp16.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:GOOD ONE - direct_tensorrt_fp16.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\GOOD ONE TO USE - onnx_create plus ultra.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:GOOD ONE TO USE - onnx_create plus ultra.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\configs\\extra_dataset\\Plus_Ultra_LR.yaml||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:configs\\extra_dataset\\Plus_Ultra_LR.yaml||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{00000000-0000-0000-0000-000000000000}|<Solution>|InSPyReNet||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\fix_1280x720_fp16_nan.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:fix_1280x720_fp16_nan.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\manual_fp16_conversion.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:manual_fp16_conversion.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\InferenceTensorRT.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:InferenceTensorRT.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\verify_tensorrt_fp16_success.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:verify_tensorrt_fp16_success.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\run_fp16_test.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:run_fp16_test.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\convert_onnx_to_tensorrt.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:convert_onnx_to_tensorrt.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\requirements.txt||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:requirements.txt||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\lib\\backbones\\SwinTransformer.py||{8B382828-6202-11D1-8870-0000F87579D2}|", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:lib\\backbones\\SwinTransformer.py||{8B382828-6202-11D1-8870-0000F87579D2}|"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\utils\\misc.py||{8B382828-6202-11D1-8870-0000F87579D2}|", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:utils\\misc.py||{8B382828-6202-11D1-8870-0000F87579D2}|"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\use_onnx - tensorrt.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:use_onnx - tensorrt.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\use_onnx - cuda.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:use_onnx - cuda.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\use_onnx - tensorrt batched.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:use_onnx - tensorrt batched.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\use_onnx.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:use_onnx.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\data\\custom_transforms.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:data\\custom_transforms.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\configs\\extra_dataset\\Plus_Ultra.yaml||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:configs\\extra_dataset\\Plus_Ultra.yaml||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\README.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:README.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\onnx_create plus ultra LR.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:onnx_create plus ultra LR.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\fix_fp16_type_mismatch.py||{8B382828-6202-11D1-8870-0000F87579D2}|", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:fix_fp16_type_mismatch.py||{8B382828-6202-11D1-8870-0000F87579D2}|"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\test_corrected_fp16_models.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:test_corrected_fp16_models.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\onnx_convert plus ultra models to fp16 buggy.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:onnx_convert plus ultra models to fp16 buggy.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\use_onnx - cuda - fp16.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:use_onnx - cuda - fp16.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\run\\Test.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:run\\Test.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\run\\Inference.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:run\\Inference.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\run\\Inference with Tensor Cores.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:run\\Inference with Tensor Cores.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\lib\\InSPyReNet.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:lib\\InSPyReNet.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\lib\\__init__.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:lib\\__init__.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\onnx_optimize for cuda multibatches.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:onnx_optimize for cuda multibatches.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\configs\\extra_dataset\\InSPyReNet_SwinB_DH.yaml||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:configs\\extra_dataset\\InSPyReNet_SwinB_DH.yaml||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\onnx_test_with_dml.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:onnx_test_with_dml.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\utils\\download.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:utils\\download.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\configs\\extra_dataset\\InSPyReNet_SwinB_DHU_LR.yaml||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:configs\\extra_dataset\\InSPyReNet_SwinB_DHU_LR.yaml||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\utils\\eval_functions.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:utils\\eval_functions.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\data\\dataloader.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:data\\dataloader.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\onnx_optimize plus ultra for cuda.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:onnx_optimize plus ultra for cuda.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\Important.txt||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Important.txt||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\run\\Eval.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:run\\Eval.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\onnx_optimize for dml.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:onnx_optimize for dml.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\run\\Inference with Tensor Cores and Compile.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:run\\Inference with Tensor Cores and Compile.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\use_onnx - cuda batched.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:use_onnx - cuda batched.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\convert_to_tensorrt with freezing.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:convert_to_tensorrt with freezing.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\convert_to_tensorrt with Torchscript.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:convert_to_tensorrt with Torchscript.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\torch2trt\\torch2trt\\dataset.py||{8B382828-6202-11D1-8870-0000F87579D2}|", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:torch2trt\\torch2trt\\dataset.py||{8B382828-6202-11D1-8870-0000F87579D2}|"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\convert_to_tensorrt Directly.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:convert_to_tensorrt Directly.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\run\\__init__.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:run\\__init__.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\Expr.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:Expr.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\configs\\extra_dataset\\InSPyReNet_SwinB_HU.yaml||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:configs\\extra_dataset\\InSPyReNet_SwinB_HU.yaml||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\torchscript_infer.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:torchscript_infer.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\onnx_optimize for cuda old.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:onnx_optimize for cuda old.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\regenerate_tensorrt_engine.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:regenerate_tensorrt_engine.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\regenerate_fp16_safe_models.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:regenerate_fp16_safe_models.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\InSPyReNet.pyproj||{B270807C-D8C6-49EB-8EBE-8E8D566637A1}|9a46bc86-34cb-4597-83e5-498e3bdba20a", "RelativeMoniker": "D:0:0:{3738AB56-3776-4BFA-B9DC-9D47412A5003}|InSPyReNet.pyproj|solutionrelative:InSPyReNet.pyproj||{B270807C-D8C6-49EB-8EBE-8E8D566637A1}|9a46bc86-34cb-4597-83e5-498e3bdba20a"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Document", "DocumentIndex": 2, "Title": "Plus_Ultra_LR.yaml", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\configs\\extra_dataset\\Plus_Ultra_LR.yaml", "RelativeDocumentMoniker": "configs\\extra_dataset\\Plus_Ultra_LR.yaml", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\configs\\extra_dataset\\Plus_Ultra_LR.yaml", "RelativeToolTip": "configs\\extra_dataset\\Plus_Ultra_LR.yaml", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003775|", "WhenOpened": "2025-06-25T22:23:15.749Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "fix_1280x720_fp16_nan.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\fix_1280x720_fp16_nan.py", "RelativeDocumentMoniker": "fix_1280x720_fp16_nan.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\fix_1280x720_fp16_nan.py", "RelativeToolTip": "fix_1280x720_fp16_nan.py", "ViewState": "AgIAAO8AAAAAAAAAAAAAAAABAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-25T20:37:47.951Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "GOOD ONE - direct_tensorrt_fp16.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\GOOD ONE - direct_tensorrt_fp16.py", "RelativeDocumentMoniker": "GOOD ONE - direct_tensorrt_fp16.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\GOOD ONE - direct_tensorrt_fp16.py", "RelativeToolTip": "GOOD ONE - direct_tensorrt_fp16.py", "ViewState": "AgIAACYDAAAAAAAAAAAcwEMDAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-25T17:23:27.817Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "run_fp16_test.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\run_fp16_test.py", "RelativeDocumentMoniker": "run_fp16_test.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\run_fp16_test.py", "RelativeToolTip": "run_fp16_test.py", "ViewState": "AgIAAPEAAAAAAAAAAIAwwAQBAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-25T16:50:42.156Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 53, "Title": "regenerate_fp16_safe_models.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\regenerate_fp16_safe_models.py", "RelativeDocumentMoniker": "regenerate_fp16_safe_models.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\regenerate_fp16_safe_models.py", "RelativeToolTip": "regenerate_fp16_safe_models.py", "ViewState": "AgIAAEwAAAAAAAAAAAAgwGAAAABNAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-25T15:32:01.571Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "GOOD ONE TO USE - onnx_create plus ultra.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\GOOD ONE TO USE - onnx_create plus ultra.py", "RelativeDocumentMoniker": "GOOD ONE TO USE - onnx_create plus ultra.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\GOOD ONE TO USE - onnx_create plus ultra.py", "RelativeToolTip": "GOOD ONE TO USE - onnx_create plus ultra.py", "ViewState": "AgIAADIAAAAAAAAAAADwv0sAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-22T12:42:47.027Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "InSPyReNet", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\InSPyReNet.pyproj", "RelativeDocumentMoniker": "InSPyReNet.pyproj", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\InSPyReNet.pyproj*", "RelativeToolTip": "InSPyReNet.pyproj*", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-06-25T18:09:31.448Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "verify_tensorrt_fp16_success.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\verify_tensorrt_fp16_success.py", "RelativeDocumentMoniker": "verify_tensorrt_fp16_success.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\verify_tensorrt_fp16_success.py", "RelativeToolTip": "verify_tensorrt_fp16_success.py", "ViewState": "AgIAABsAAAAAAAAAAIAwwDYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-25T18:04:55.43Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "manual_fp16_conversion.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\manual_fp16_conversion.py", "RelativeDocumentMoniker": "manual_fp16_conversion.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\manual_fp16_conversion.py", "RelativeToolTip": "manual_fp16_conversion.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-25T18:20:18.318Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "Plus_Ultra.yaml", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\configs\\extra_dataset\\Plus_Ultra.yaml", "RelativeDocumentMoniker": "configs\\extra_dataset\\Plus_Ultra.yaml", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\configs\\extra_dataset\\Plus_Ultra.yaml", "RelativeToolTip": "configs\\extra_dataset\\Plus_Ultra.yaml", "ViewState": "AgIAAC0AAAAAAAAAAIBJwAEAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003775|", "WhenOpened": "2025-05-22T13:58:48.719Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "README.md", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\README.md", "RelativeDocumentMoniker": "README.md", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\README.md", "RelativeToolTip": "README.md", "ViewState": "AgIAAHoAAAAAAAAAAAAAALoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-05-22T12:43:47.65Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "InferenceTensorRT.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\InferenceTensorRT.py", "RelativeDocumentMoniker": "InferenceTensorRT.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\InferenceTensorRT.py", "RelativeToolTip": "InferenceTensorRT.py", "ViewState": "AgIAANcBAAAAAAAAAIAwwPIBAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-19T17:47:27.33Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "custom_transforms.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\data\\custom_transforms.py", "RelativeDocumentMoniker": "data\\custom_transforms.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\data\\custom_transforms.py", "RelativeToolTip": "data\\custom_transforms.py", "ViewState": "AgIAACEAAAAAAAAAAAAWwCsAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-22T00:14:10.848Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "use_onnx.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\use_onnx.py", "RelativeDocumentMoniker": "use_onnx.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\use_onnx.py", "RelativeToolTip": "use_onnx.py", "ViewState": "AgIAADkAAAAAAAAAAAAmwEoAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-21T12:11:29.414Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "use_onnx - cuda.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\use_onnx - cuda.py", "RelativeDocumentMoniker": "use_onnx - cuda.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\use_onnx - cuda.py", "RelativeToolTip": "use_onnx - cuda.py", "ViewState": "AgIAAF4AAAAAAAAAAAAiwHYAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-19T18:35:13.946Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "SwinTransformer.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\lib\\backbones\\SwinTransformer.py", "RelativeDocumentMoniker": "lib\\backbones\\SwinTransformer.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\lib\\backbones\\SwinTransformer.py", "RelativeToolTip": "lib\\backbones\\SwinTransformer.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAABoAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-21T02:30:32.649Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "requirements.txt", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\requirements.txt", "RelativeDocumentMoniker": "requirements.txt", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\requirements.txt", "RelativeToolTip": "requirements.txt", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003109|", "WhenOpened": "2025-05-21T00:42:10.065Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "convert_onnx_to_tensorrt.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\convert_onnx_to_tensorrt.py", "RelativeDocumentMoniker": "convert_onnx_to_tensorrt.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\convert_onnx_to_tensorrt.py", "RelativeToolTip": "convert_onnx_to_tensorrt.py", "ViewState": "AgIAAEgAAAAAAAAAAAAcwGMAAABPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-19T18:09:43.309Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 35, "Title": "eval_functions.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\utils\\eval_functions.py", "RelativeDocumentMoniker": "utils\\eval_functions.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\utils\\eval_functions.py", "RelativeToolTip": "utils\\eval_functions.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-23T19:22:26.078Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "use_onnx - tensorrt.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\use_onnx - tensorrt.py", "RelativeDocumentMoniker": "use_onnx - tensorrt.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\use_onnx - tensorrt.py", "RelativeToolTip": "use_onnx - tensorrt.py", "ViewState": "AgIAAJ4AAAAAAAAAAAAwwL4AAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-20T15:11:01.857Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "misc.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\utils\\misc.py", "RelativeDocumentMoniker": "utils\\misc.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\utils\\misc.py", "RelativeToolTip": "utils\\misc.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAABwAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-21T00:57:18.079Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "use_onnx - tensorrt batched.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\use_onnx - tensorrt batched.py", "RelativeDocumentMoniker": "use_onnx - tensorrt batched.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\use_onnx - tensorrt batched.py", "RelativeToolTip": "use_onnx - tensorrt batched.py", "ViewState": "AgIAAFgAAAAAAAAAAIBJwI0AAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-17T09:14:35.728Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "onnx_create plus ultra LR.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\onnx_create plus ultra LR.py", "RelativeDocumentMoniker": "onnx_create plus ultra LR.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\onnx_create plus ultra LR.py", "RelativeToolTip": "onnx_create plus ultra LR.py", "ViewState": "AgIAAGIAAAAAAAAAAAAAwHUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-19T22:29:34.096Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 24, "Title": "use_onnx - cuda - fp16.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\use_onnx - cuda - fp16.py", "RelativeDocumentMoniker": "use_onnx - cuda - fp16.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\use_onnx - cuda - fp16.py", "RelativeToolTip": "use_onnx - cuda - fp16.py", "ViewState": "AgIAAG0AAAAAAAAAAAAAwIEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-19T19:38:26.163Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "fix_fp16_type_mismatch.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\fix_fp16_type_mismatch.py", "RelativeDocumentMoniker": "fix_fp16_type_mismatch.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\fix_fp16_type_mismatch.py", "RelativeToolTip": "fix_fp16_type_mismatch.py", "ViewState": "AgIAAKAAAAAAAAAAAAAkwLwAAABGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-19T21:09:01.499Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "test_corrected_fp16_models.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\test_corrected_fp16_models.py", "RelativeDocumentMoniker": "test_corrected_fp16_models.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\test_corrected_fp16_models.py", "RelativeToolTip": "test_corrected_fp16_models.py", "ViewState": "AgIAAMkAAAAAAAAAAAAQwOAAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-19T21:05:29.878Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "onnx_convert plus ultra models to fp16 buggy.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\onnx_convert plus ultra models to fp16 buggy.py", "RelativeDocumentMoniker": "onnx_convert plus ultra models to fp16 buggy.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\onnx_convert plus ultra models to fp16 buggy.py", "RelativeToolTip": "onnx_convert plus ultra models to fp16 buggy.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-19T19:14:07.905Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 51, "Title": "onnx_optimize for cuda old.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\onnx_optimize for cuda old.py", "RelativeDocumentMoniker": "onnx_optimize for cuda old.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\onnx_optimize for cuda old.py", "RelativeToolTip": "onnx_optimize for cuda old.py", "ViewState": "AgIAAM8AAAAAAAAAAAAmwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-19T19:11:19.674Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 26, "Title": "Inference.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\run\\Inference.py", "RelativeDocumentMoniker": "run\\Inference.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\run\\Inference.py", "RelativeToolTip": "run\\Inference.py", "ViewState": "AgIAAAAAAAAAAAAAAAAxwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-19T18:27:18.78Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 25, "Title": "Test.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\run\\Test.py", "RelativeDocumentMoniker": "run\\Test.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\run\\Test.py", "RelativeToolTip": "run\\Test.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-19T18:27:30.805Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 34, "Title": "InSPyReNet_SwinB_DHU_LR.yaml", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\configs\\extra_dataset\\InSPyReNet_SwinB_DHU_LR.yaml", "RelativeDocumentMoniker": "configs\\extra_dataset\\InSPyReNet_SwinB_DHU_LR.yaml", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\configs\\extra_dataset\\InSPyReNet_SwinB_DHU_LR.yaml", "RelativeToolTip": "configs\\extra_dataset\\InSPyReNet_SwinB_DHU_LR.yaml", "ViewState": "AgIAACcAAAAAAAAAAAAtwDkAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003775|", "WhenOpened": "2025-05-23T19:21:40.356Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 52, "Title": "regenerate_tensorrt_engine.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\regenerate_tensorrt_engine.py", "RelativeDocumentMoniker": "regenerate_tensorrt_engine.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\regenerate_tensorrt_engine.py", "RelativeToolTip": "regenerate_tensorrt_engine.py", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-19T18:07:17.209Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "InSPyReNet.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\lib\\InSPyReNet.py", "RelativeDocumentMoniker": "lib\\InSPyReNet.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\lib\\InSPyReNet.py", "RelativeToolTip": "lib\\InSPyReNet.py", "ViewState": "AgIAAFoAAAAAAAAAAAAlwA4AAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-16T15:30:52.234Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 27, "Title": "Inference with Tensor Cores.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\run\\Inference with Tensor Cores.py", "RelativeDocumentMoniker": "run\\Inference with Tensor Cores.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\run\\Inference with Tensor Cores.py", "RelativeToolTip": "run\\Inference with Tensor Cores.py", "ViewState": "AgIAAAYAAAAAAAAAAIBJwBsAAABQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-21T12:08:56.721Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 33, "Title": "download.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\utils\\download.py", "RelativeDocumentMoniker": "utils\\download.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\utils\\download.py", "RelativeToolTip": "utils\\download.py", "ViewState": "AgIAAJMAAAAAAAAAAIBJwJkAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-23T19:23:16.031Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 50, "Title": "torchscript_infer.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\torchscript_infer.py", "RelativeDocumentMoniker": "torchscript_infer.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\torchscript_infer.py", "RelativeToolTip": "torchscript_infer.py", "ViewState": "AgIAAEUAAAAAAAAAAAAAAGQAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-28T20:32:26.742Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 29, "Title": "__init__.py (lib)", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\lib\\__init__.py", "RelativeDocumentMoniker": "lib\\__init__.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\lib\\__init__.py", "RelativeToolTip": "lib\\__init__.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-28T20:34:45.48Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 31, "Title": "InSPyReNet_SwinB_DH.yaml", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\configs\\extra_dataset\\InSPyReNet_SwinB_DH.yaml", "RelativeDocumentMoniker": "configs\\extra_dataset\\InSPyReNet_SwinB_DH.yaml", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\configs\\extra_dataset\\InSPyReNet_SwinB_DH.yaml", "RelativeToolTip": "configs\\extra_dataset\\InSPyReNet_SwinB_DH.yaml", "ViewState": "AgIAAAQAAAAAAAAAAAAIwBoAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003775|", "WhenOpened": "2025-05-23T19:24:43.458Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 30, "Title": "onnx_optimize for cuda multibatches.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\onnx_optimize for cuda multibatches.py", "RelativeDocumentMoniker": "onnx_optimize for cuda multibatches.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\onnx_optimize for cuda multibatches.py", "RelativeToolTip": "onnx_optimize for cuda multibatches.py", "ViewState": "AgIAAGwAAAAAAAAAAAAIwHkAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-21T18:21:51.756Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 36, "Title": "dataloader.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\data\\dataloader.py", "RelativeDocumentMoniker": "data\\dataloader.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\data\\dataloader.py", "RelativeToolTip": "data\\dataloader.py", "ViewState": "AgIAAEMAAAAAAAAAAAAYwFQAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-22T13:56:36.436Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 32, "Title": "onnx_test_with_dml.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\onnx_test_with_dml.py", "RelativeDocumentMoniker": "onnx_test_with_dml.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\onnx_test_with_dml.py", "RelativeToolTip": "onnx_test_with_dml.py", "ViewState": "AgIAABAAAAAAAAAAAAAnwBkAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-21T14:12:47.022Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 37, "Title": "onnx_optimize plus ultra for cuda.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\onnx_optimize plus ultra for cuda.py", "RelativeDocumentMoniker": "onnx_optimize plus ultra for cuda.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\onnx_optimize plus ultra for cuda.py", "RelativeToolTip": "onnx_optimize plus ultra for cuda.py", "ViewState": "AgIAAG8BAAAAAAAAAIBJwI4BAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-22T17:29:54.83Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 40, "Title": "onnx_optimize for dml.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\onnx_optimize for dml.py", "RelativeDocumentMoniker": "onnx_optimize for dml.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\onnx_optimize for dml.py", "RelativeToolTip": "onnx_optimize for dml.py", "ViewState": "AgIAAIABAACAQOcW+s8qwIsBAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-21T15:19:49.406Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 38, "Title": "Important.txt", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\Important.txt", "RelativeDocumentMoniker": "Important.txt", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\Important.txt", "RelativeToolTip": "Important.txt", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003109|", "WhenOpened": "2025-05-22T14:01:24.778Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 39, "Title": "Eval.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\run\\Eval.py", "RelativeDocumentMoniker": "run\\Eval.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\run\\Eval.py", "RelativeToolTip": "run\\Eval.py", "ViewState": "AgIAAGkAAAAAAAAAAIBZwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-16T17:43:11.076Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 42, "Title": "use_onnx - cuda batched.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\use_onnx - cuda batched.py", "RelativeDocumentMoniker": "use_onnx - cuda batched.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\use_onnx - cuda batched.py", "RelativeToolTip": "use_onnx - cuda batched.py", "ViewState": "AgIAAC8AAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-21T12:15:56.365Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 41, "Title": "Inference with Tensor Cores and Compile.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\run\\Inference with Tensor Cores and Compile.py", "RelativeDocumentMoniker": "run\\Inference with Tensor Cores and Compile.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\run\\Inference with Tensor Cores and Compile.py", "RelativeToolTip": "run\\Inference with Tensor Cores and Compile.py", "ViewState": "AgIAAAoBAAAAAAAAAIBZwB8BAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-21T12:19:40.665Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 43, "Title": "convert_to_tensorrt with freezing.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\convert_to_tensorrt with freezing.py", "RelativeDocumentMoniker": "convert_to_tensorrt with freezing.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\convert_to_tensorrt with freezing.py", "RelativeToolTip": "convert_to_tensorrt with freezing.py", "ViewState": "AgIAAKQBAAAAAAAAAADwv68BAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-21T01:36:37.293Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 46, "Title": "convert_to_tensorrt Directly.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\convert_to_tensorrt Directly.py", "RelativeDocumentMoniker": "convert_to_tensorrt Directly.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\convert_to_tensorrt Directly.py", "RelativeToolTip": "convert_to_tensorrt Directly.py", "ViewState": "AgIAAJsAAAAAAAAAAIBZwKYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-21T00:21:01.671Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 44, "Title": "convert_to_tensorrt with Torchscript.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\convert_to_tensorrt with Torchscript.py", "RelativeDocumentMoniker": "convert_to_tensorrt with Torchscript.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\convert_to_tensorrt with Torchscript.py", "RelativeToolTip": "convert_to_tensorrt with Torchscript.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-21T02:44:35.317Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 45, "Title": "dataset.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\torch2trt\\torch2trt\\dataset.py", "RelativeDocumentMoniker": "torch2trt\\torch2trt\\dataset.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\torch2trt\\torch2trt\\dataset.py", "RelativeToolTip": "torch2trt\\torch2trt\\dataset.py", "ViewState": "AgIAAFsBAAAAAAAAAAAAAGIBAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-21T09:23:43.926Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 47, "Title": "__init__.py (run)", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\run\\__init__.py", "RelativeDocumentMoniker": "run\\__init__.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\run\\__init__.py", "RelativeToolTip": "run\\__init__.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-16T17:43:13.706Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 48, "Title": "Expr.py", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\Expr.py", "RelativeDocumentMoniker": "Expr.py", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\Expr.py", "RelativeToolTip": "Expr.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-16T17:43:05.633Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 49, "Title": "InSPyReNet_SwinB_HU.yaml", "DocumentMoniker": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\configs\\extra_dataset\\InSPyReNet_SwinB_HU.yaml", "RelativeDocumentMoniker": "configs\\extra_dataset\\InSPyReNet_SwinB_HU.yaml", "ToolTip": "F:\\Catechese\\EditeurAudioVideo\\Archives\\InSPyReNet\\configs\\extra_dataset\\InSPyReNet_SwinB_HU.yaml", "RelativeToolTip": "configs\\extra_dataset\\InSPyReNet_SwinB_HU.yaml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003775|", "WhenOpened": "2025-05-16T17:38:12.363Z", "EditorCaption": ""}]}]}]}