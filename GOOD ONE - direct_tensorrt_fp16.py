#!/usr/bin/env python3
"""
Direct TensorRT FP16 Conversion - Updated for TensorRT 10.x

This script converts FP32 ONNX models directly to TensorRT FP16 engines
and compares the results with FP32 models. TensorRT handles the FP16 
conversion internally with better mixed precision management.

Compatible with TensorRT 10.x and newer versions.

Usage:
    conda activate tensorrt_env
    python direct_tensorrt_fp16.py
"""

import os
import sys
import subprocess
import time
import numpy as np
from PIL import Image
import tensorrt as trt

# Try to import CUDA-related libraries
try:
    import torch
    TORCH_AVAILABLE = True
    CUDA_AVAILABLE = torch.cuda.is_available() if hasattr(torch.cuda, 'is_available') else False
except ImportError:
    TORCH_AVAILABLE = False
    CUDA_AVAILABLE = False

try:
    import pycuda.driver as cuda
    import pycuda.autoinit
    PYCUDA_AVAILABLE = True
except ImportError:
    PYCUDA_AVAILABLE = False

print(f"CUDA Environment Check:")
print(f"  PyTorch available: {TORCH_AVAILABLE}")
print(f"  CUDA available: {CUDA_AVAILABLE}")
print(f"  PyCUDA available: {PYCUDA_AVAILABLE}")

def test_fp32_onnx_model(model_path, test_image="input.png"):
    """
    Test the FP32 ONNX model to ensure it works before TensorRT conversion
    """
    print(f"Testing FP32 ONNX model: {model_path}")
    
    try:
        import onnxruntime as ort
        import numpy as np
        from PIL import Image
        
        if not os.path.exists(model_path):
            print(f"❌ Model not found: {model_path}")
            return False
            
        if not os.path.exists(test_image):
            print(f"❌ Test image not found: {test_image}")
            return False
        
        # Load model
        print("Loading FP32 ONNX model...")
        providers = ['CPUExecutionProvider']
        session = ort.InferenceSession(model_path, providers=providers)
        print("✓ FP32 ONNX model loaded successfully")
        
        # Get input info
        input_info = session.get_inputs()[0]
        input_shape = input_info.shape
        
        # Determine target size
        if len(input_shape) >= 4:
            target_size = (input_shape[3], input_shape[2])
        else:
            target_size = (720, 1280)
        
        # Preprocess image
        print("Preprocessing image...")
        img = Image.open(test_image).convert('RGB')
        img = img.resize(target_size, Image.BILINEAR)
        
        # Convert to numpy array as float32
        img = np.array(img, dtype=np.float32) / 255.0
        
        # Normalization with explicit float32 arrays
        mean = np.array([0.485, 0.456, 0.406], dtype=np.float32)
        std = np.array([0.229, 0.224, 0.225], dtype=np.float32)
        img = (img - mean) / std
        
        # Transpose and add batch dimension
        img = np.transpose(img, (2, 0, 1))
        img = np.expand_dims(img, axis=0)
        img = img.astype(np.float32)
        
        # Run inference
        print("Running FP32 inference...")
        outputs = session.run(None, {input_info.name: img})
        output = outputs[0].squeeze()
        
        print("✓ FP32 inference completed successfully")
        
        # Analyze output
        output_min = np.min(output)
        output_max = np.max(output)
        output_mean = np.mean(output)
        output_std = np.std(output)
        
        # Check for issues
        nan_count = np.isnan(output).sum()
        inf_count = np.isinf(output).sum()
        white_pixels = np.sum(output > 0.95)
        total_pixels = output.size
        white_percentage = white_pixels / total_pixels * 100
        
        print(f"\nFP32 Output Analysis:")
        print(f"  Range: [{output_min:.4f}, {output_max:.4f}]")
        print(f"  Mean: {output_mean:.4f}, Std: {output_std:.4f}")
        print(f"  White pixels (>0.95): {white_percentage:.1f}%")
        print(f"  NaN: {nan_count}, Inf: {inf_count}")
        
        # Determine if model is working correctly
        if nan_count > 0 or inf_count > 0:
            print("❌ FAIL: Contains NaN or Inf values")
            return False
        elif white_percentage > 95:
            print("❌ FAIL: White alpha matte issue detected")
            return False
        elif output_std < 0.01:
            print("❌ FAIL: No contrast (flat output)")
            return False
        else:
            print("✅ PASS: FP32 model output looks good")
            
            # Save test output for comparison
            output_img = (output * 255).astype(np.uint8)
            test_output_path = "test_output_fp32_reference.png"
            Image.fromarray(output_img).save(test_output_path)
            print(f"✓ FP32 reference output saved: {test_output_path}")
            
            return True
            
    except Exception as e:
        print(f"❌ FP32 test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def convert_fp32_to_tensorrt_fp16(onnx_model_path):
    """
    Convert FP32 ONNX directly to TensorRT FP16 using TensorRT 10.x API
    """
    print(f"\nConverting FP32 ONNX to TensorRT FP16: {onnx_model_path}")
    
    engine_path = onnx_model_path.replace('.onnx', '_fp16.engine')
    
    if os.path.exists(engine_path):
        print(f"✅ TensorRT FP16 engine already exists: {engine_path}")
        return engine_path
    
    try:
        # First try using trtexec (recommended for TensorRT 10.x)
        trtexec_success = convert_with_trtexec(onnx_model_path, engine_path, use_fp16=True)
        if trtexec_success:
            return engine_path
        
        # Fallback to Python API conversion
        print("Falling back to Python API conversion...")
        return convert_with_python_api(onnx_model_path, engine_path, use_fp16=True)
            
    except Exception as e:
        print(f"❌ TensorRT conversion error: {e}")
        return None

def convert_fp32_to_tensorrt_fp32(onnx_model_path):
    """
    Convert FP32 ONNX to TensorRT FP32 for comparison
    """
    print(f"\nConverting FP32 ONNX to TensorRT FP32: {onnx_model_path}")
    
    engine_path = onnx_model_path.replace('.onnx', '_fp32.engine')
    
    if os.path.exists(engine_path):
        print(f"✅ TensorRT FP32 engine already exists: {engine_path}")
        return engine_path
    
    try:
        # First try using trtexec
        trtexec_success = convert_with_trtexec(onnx_model_path, engine_path, use_fp16=False)
        if trtexec_success:
            return engine_path
        
        # Fallback to Python API conversion
        print("Falling back to Python API conversion...")
        return convert_with_python_api(onnx_model_path, engine_path, use_fp16=False)
            
    except Exception as e:
        print(f"❌ TensorRT FP32 conversion error: {e}")
        return None

def find_trtexec():
    """Find trtexec executable for TensorRT 10.x"""
    possible_paths = [
        "trtexec",
        "trtexec.exe",
        r"C:\Program Files\NVIDIA GPU Computing Toolkit\TensorRT\bin\trtexec.exe",
        r"C:\Program Files\NVIDIA Corporation\NVIDIA TensorRT\bin\trtexec.exe",
        "/usr/src/tensorrt/bin/trtexec",
        "/opt/tensorrt/bin/trtexec",
    ]
    
    for path in possible_paths:
        try:
            result = subprocess.run([path, "--help"], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                return path
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            continue
    
    return None

def convert_with_trtexec(onnx_path, engine_path, use_fp16=True):
    """Convert using trtexec (recommended for TensorRT 10.x)"""
    trtexec_path = find_trtexec()
    if not trtexec_path:
        print("⚠️ trtexec not found, will use Python API")
        return False
    
    print(f"✓ Found trtexec: {trtexec_path}")
    
    # Build command for TensorRT 10.x
    cmd = [
        trtexec_path,
        f"--onnx={onnx_path}",
        f"--saveEngine={engine_path}",
        "--memPoolSize=workspace:2048",  # TensorRT 10.x syntax
        "--verbose"
    ]
    
    if use_fp16:
        cmd.append("--fp16")
        print("Using FP16 precision")
    else:
        print("Using FP32 precision")
    
    print(f"Running: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=3000)
        
        if result.returncode == 0 and os.path.exists(engine_path):
            engine_size = os.path.getsize(engine_path) / (1024 * 1024)
            onnx_size = os.path.getsize(onnx_path) / (1024 * 1024)
            precision = "FP16" if use_fp16 else "FP32"
            print(f"✅ TensorRT {precision} conversion successful!")
            print(f"✓ Engine created: {engine_path}")
            print(f"✓ Size: ONNX {onnx_size:.1f}MB → TensorRT {engine_size:.1f}MB")
            return True
        else:
            print("❌ trtexec conversion failed")
            if result.stderr:
                print("Error:", result.stderr[:500])
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ trtexec conversion timed out")
        return False
    except Exception as e:
        print(f"❌ trtexec error: {e}")
        return False

def convert_with_python_api(onnx_path, engine_path, use_fp16=True):
    """Convert using TensorRT Python API (TensorRT 10.x compatible)"""
    try:
        # TensorRT 10.x Logger
        TRT_LOGGER = trt.Logger(trt.Logger.WARNING)
        
        # Create builder and network
        builder = trt.Builder(TRT_LOGGER)
        network = builder.create_network(1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))
        parser = trt.OnnxParser(network, TRT_LOGGER)
        
        # Parse ONNX model
        print("Parsing ONNX model...")
        with open(onnx_path, 'rb') as model:
            if not parser.parse(model.read()):
                print("❌ Failed to parse ONNX model")
                for error in range(parser.num_errors):
                    print(parser.get_error(error))
                return None
        
        # Configure builder
        config = builder.create_builder_config()
        
        # Set memory pool size (TensorRT 10.x syntax)
        config.set_memory_pool_limit(trt.MemoryPoolType.WORKSPACE, 2 << 30)  # 2GB
        
        if use_fp16:
            if builder.platform_has_fast_fp16:
                config.set_flag(trt.BuilderFlag.FP16)
                print("✓ FP16 optimization enabled")
            else:
                print("⚠️ FP16 not supported on this platform, using FP32")
        
        # Build engine
        print("Building TensorRT engine...")
        serialized_engine = builder.build_serialized_network(network, config)
        
        if serialized_engine is None:
            print("❌ Failed to build TensorRT engine")
            return None
        
        # Save engine
        with open(engine_path, 'wb') as f:
            f.write(serialized_engine)
        
        engine_size = os.path.getsize(engine_path) / (1024 * 1024)
        onnx_size = os.path.getsize(onnx_path) / (1024 * 1024)
        precision = "FP16" if use_fp16 else "FP32"
        print(f"✅ TensorRT {precision} engine built successfully!")
        print(f"✓ Engine saved: {engine_path}")
        print(f"✓ Size: ONNX {onnx_size:.1f}MB → TensorRT {engine_size:.1f}MB")
        
        return engine_path
        
    except Exception as e:
        print(f"❌ Python API conversion failed: {e}")
        return None

def test_tensorrt_engine(engine_path, precision="FP16", test_image="input.png"):
    """
    Test TensorRT engine with fallback for different CUDA environments
    """
    output_suffix = precision.lower()
    output_file = f"test_output_tensorrt_{output_suffix}.png"
    
    print(f"\nTesting TensorRT {precision} engine: {engine_path}")
    
    try:
        # Load and test engine directly using TensorRT API
        TRT_LOGGER = trt.Logger(trt.Logger.WARNING)
        
        # Load engine
        with open(engine_path, 'rb') as f:
            engine_data = f.read()
        
        runtime = trt.Runtime(TRT_LOGGER)
        engine = runtime.deserialize_cuda_engine(engine_data)
        
        if engine is None:
            print(f"❌ Failed to load {precision} engine")
            return False, None
        
        context = engine.create_execution_context()
        print(f"✓ {precision} engine loaded successfully")
        
        # Get input/output info
        input_binding = 0
        output_binding = 1
        
        input_shape = engine.get_tensor_shape(engine.get_tensor_name(input_binding))
        output_shape = engine.get_tensor_shape(engine.get_tensor_name(output_binding))
        
        print(f"Input shape: {input_shape}")
        print(f"Output shape: {output_shape}")
        
        # Prepare input
        if not os.path.exists(test_image):
            print(f"❌ Test image not found: {test_image}")
            return False, None
        
        # Load and preprocess image
        img = Image.open(test_image).convert('RGB')
        target_size = (input_shape[3], input_shape[2])  # W, H
        img = img.resize(target_size, Image.BILINEAR)
        
        # Convert to numpy and normalize
        img_array = np.array(img, dtype=np.float32) / 255.0
        mean = np.array([0.485, 0.456, 0.406], dtype=np.float32)
        std = np.array([0.229, 0.224, 0.225], dtype=np.float32)
        img_array = (img_array - mean) / std
        
        # Transpose and add batch dimension
        img_array = np.transpose(img_array, (2, 0, 1))
        img_array = np.expand_dims(img_array, axis=0)
        
        # Try different CUDA memory allocation approaches
        if TORCH_AVAILABLE and CUDA_AVAILABLE:
            return test_with_torch_cuda(context, engine, img_array, input_shape, output_shape, precision, output_file)
        elif PYCUDA_AVAILABLE:
            result = test_with_pycuda(context, engine, img_array, input_shape, output_shape, precision, output_file)
            if result[0]:  # If PyCUDA succeeded
                return result
            else:
                print("PyCUDA failed, trying fallback method...")
                return test_with_inference_script(engine_path, precision, test_image)
        else:
            print("❌ No direct CUDA support available.")
            print("Trying fallback method using existing inference script...")
            return test_with_inference_script(engine_path, precision, test_image)
        
    except Exception as e:
        print(f"❌ {precision} engine test error: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_with_torch_cuda(context, engine, img_array, input_shape, output_shape, precision, output_file):
    """Test using PyTorch CUDA tensors"""
    try:
        # Convert TensorRT Dims to tuple for PyTorch
        if hasattr(output_shape, '__len__'):
            output_shape_tuple = tuple(output_shape)
        else:
            # Fallback if output_shape is not iterable
            output_shape_tuple = (1, 1, 1024, 1024)

        # Allocate GPU memory using PyTorch
        input_tensor = torch.from_numpy(img_array).cuda()
        output_tensor = torch.empty(output_shape_tuple, dtype=torch.float32, device='cuda')
        
        # Set tensor addresses
        context.set_tensor_address(engine.get_tensor_name(0), input_tensor.data_ptr())
        context.set_tensor_address(engine.get_tensor_name(1), output_tensor.data_ptr())
        
        # Run inference with timing
        torch.cuda.synchronize()
        start_time = time.time()
        
        success = context.execute_async_v3(torch.cuda.current_stream().cuda_stream)
        torch.cuda.synchronize()
        
        end_time = time.time()
        inference_time = (end_time - start_time) * 1000  # ms
        
        if not success:
            print(f"❌ {precision} inference failed")
            return False, None
        
        print(f"✅ {precision} inference successful! ({inference_time:.2f}ms)")
        
        # Process output
        output = output_tensor.cpu().numpy().squeeze()
        
        return process_output(output, precision, output_file, inference_time)
        
    except Exception as e:
        print(f"❌ PyTorch CUDA test failed: {e}")
        return False, None

def test_with_pycuda(context, engine, img_array, input_shape, output_shape, precision, output_file):
    """Test using PyCUDA with proper type handling"""
    try:
        import pycuda.driver as cuda
        import pycuda.gpuarray as gpuarray
        
        # Calculate sizes - ensure they are Python int, not numpy int64
        input_size = int(np.prod(input_shape) * np.dtype(np.float32).itemsize)
        output_size = int(np.prod(output_shape) * np.dtype(np.float32).itemsize)
        
        print(f"Allocating GPU memory: input={input_size} bytes, output={output_size} bytes")
        
        # Allocate GPU memory with proper type casting
        d_input = cuda.mem_alloc(input_size)
        d_output = cuda.mem_alloc(output_size)
        
        # Prepare input data - ensure contiguous and correct type
        input_data = img_array.astype(np.float32)
        if not input_data.flags['C_CONTIGUOUS']:
            input_data = np.ascontiguousarray(input_data)
        
        # Copy input to GPU
        cuda.memcpy_htod(d_input, input_data)
        
        # Set tensor addresses - TensorRT expects the raw pointer addresses
        input_ptr = int(d_input)
        output_ptr = int(d_output)
        
        context.set_tensor_address(engine.get_tensor_name(0), input_ptr)
        context.set_tensor_address(engine.get_tensor_name(1), output_ptr)
        
        # Create CUDA stream
        stream = cuda.Stream()
        
        # Run inference with timing
        start_time = time.time()
        success = context.execute_async_v3(stream.handle)
        stream.synchronize()
        end_time = time.time()
        
        inference_time = (end_time - start_time) * 1000  # ms
        
        if not success:
            print(f"❌ {precision} inference failed")
            d_input.free()
            d_output.free()
            return False, None
        
        print(f"✅ {precision} inference successful! ({inference_time:.2f}ms)")
        
        # Copy output back to CPU
        output = np.empty(output_shape, dtype=np.float32)
        cuda.memcpy_dtoh(output, d_output)
        output = output.squeeze()
        
        # Clean up GPU memory
        d_input.free()
        d_output.free()
        
        return process_output(output, precision, output_file, inference_time)
        
    except ImportError as e:
        print(f"❌ PyCUDA import failed: {e}")
        print("Please install PyCUDA: pip install pycuda")
        return False, None
    except Exception as e:
        print(f"❌ PyCUDA test failed: {e}")
        print(f"Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return False, None

def test_with_inference_script(engine_path, precision, test_image):
    """Fallback method using the existing InferenceTensorRT.py script"""
    try:
        output_suffix = precision.lower()
        output_file = f"test_output_tensorrt_{output_suffix}.png"
        
        print(f"Using fallback inference script for {precision} engine...")
        
        # Use the existing TensorRT inference script
        cmd = [
            "python", "InferenceTensorRT.py",
            "--source", test_image,
            "--engine", engine_path,
            "--dest", output_file
        ]
        
        print(f"Running: {' '.join(cmd)}")
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        end_time = time.time()
        
        inference_time = (end_time - start_time) * 1000  # ms
        
        if result.returncode == 0 and os.path.exists(output_file):
            print(f"✅ {precision} inference successful! ({inference_time:.2f}ms)")
            
            # Load and analyze the output
            output_img = np.array(Image.open(output_file))
            output = output_img.astype(np.float32) / 255.0
            
            # Analyze output
            output_stats = {
                'min': float(np.min(output)),
                'max': float(np.max(output)),
                'mean': float(np.mean(output)),
                'std': float(np.std(output)),
                'inference_time_ms': inference_time
            }
            
            # Check for issues
            nan_count = np.isnan(output).sum()
            inf_count = np.isinf(output).sum()
            white_pixels = np.sum(output > 0.95)
            total_pixels = output.size
            white_percentage = white_pixels / total_pixels * 100
            
            print(f"{precision} Output Analysis (Fallback):")
            print(f"  Range: [{output_stats['min']:.4f}, {output_stats['max']:.4f}]")
            print(f"  Mean: {output_stats['mean']:.4f}, Std: {output_stats['std']:.4f}")
            print(f"  White pixels (>0.95): {white_percentage:.1f}%")
            print(f"  NaN: {nan_count}, Inf: {inf_count}")
            print(f"  Total time: {inference_time:.2f}ms")
            
            # Determine if output is valid
            is_valid = (nan_count == 0 and inf_count == 0 and 
                       white_percentage < 95 and output_stats['std'] > 0.01)
            
            if is_valid:
                print(f"✅ {precision} engine test PASSED (fallback)")
            else:
                print(f"❌ {precision} engine test FAILED (fallback)")
            
            return is_valid, output_stats
            
        else:
            print(f"❌ {precision} inference script failed")
            if result.stderr:
                print("Error:", result.stderr[:500])
            return False, None
            
    except subprocess.TimeoutExpired:
        print(f"❌ {precision} inference script timed out")
        return False, None
    except Exception as e:
        print(f"❌ Fallback inference failed: {e}")
        return False, None

def process_output(output, precision, output_file, inference_time):
    """Process and analyze the output from TensorRT inference"""
    try:
        # Apply sigmoid and normalize
        output = 1.0 / (1.0 + np.exp(-output))  # sigmoid
        output = (output - output.min()) / (output.max() - output.min() + 1e-8)
        
        # Analyze output
        output_stats = {
            'min': float(np.min(output)),
            'max': float(np.max(output)),
            'mean': float(np.mean(output)),
            'std': float(np.std(output)),
            'inference_time_ms': inference_time
        }
        
        # Check for issues
        nan_count = np.isnan(output).sum()
        inf_count = np.isinf(output).sum()
        white_pixels = np.sum(output > 0.95)
        total_pixels = output.size
        white_percentage = white_pixels / total_pixels * 100
        
        print(f"{precision} Output Analysis:")
        print(f"  Range: [{output_stats['min']:.4f}, {output_stats['max']:.4f}]")
        print(f"  Mean: {output_stats['mean']:.4f}, Std: {output_stats['std']:.4f}")
        print(f"  White pixels (>0.95): {white_percentage:.1f}%")
        print(f"  NaN: {nan_count}, Inf: {inf_count}")
        print(f"  Inference time: {inference_time:.2f}ms")
        
        # Save output
        output_img = (output * 255).astype(np.uint8)
        Image.fromarray(output_img).save(output_file)
        print(f"✓ {precision} output saved: {output_file}")
        
        # Determine if output is valid (relaxed criteria)
        nan_check = nan_count == 0
        inf_check = inf_count == 0
        white_check = white_percentage < 99
        std_check = output_stats['std'] > 0.001

        is_valid = nan_check and inf_check and white_check and std_check

        # Debug output
        print(f"Validation checks:")
        print(f"  NaN check: {nan_check} (count: {nan_count})")
        print(f"  Inf check: {inf_check} (count: {inf_count})")
        print(f"  White check: {white_check} (percentage: {white_percentage:.1f}%)")
        print(f"  Std check: {std_check} (std: {output_stats['std']:.6f} > 0.001)")

        if is_valid:
            print(f"✅ {precision} engine test PASSED")
        else:
            print(f"❌ {precision} engine test FAILED")
        
        return is_valid, output_stats
        
    except Exception as e:
        print(f"❌ Output processing failed: {e}")
        return False, None

def compare_outputs_advanced(fp32_stats, fp16_stats):
    """
    Advanced comparison of FP32 vs FP16 outputs with detailed analysis
    """
    print(f"\n{'='*60}")
    print("DETAILED FP32 vs FP16 COMPARISON")
    print('='*60)
    
    fp32_output = "test_output_tensorrt_fp32.png"
    fp16_output = "test_output_tensorrt_fp16.png"
    
    if not os.path.exists(fp32_output):
        print(f"❌ FP32 output not found: {fp32_output}")
        return False
    
    if not os.path.exists(fp16_output):
        print(f"❌ FP16 output not found: {fp16_output}")
        return False
    
    try:
        # Load both images
        img_fp32 = np.array(Image.open(fp32_output))
        img_fp16 = np.array(Image.open(fp16_output))
        
        print(f"FP32 output shape: {img_fp32.shape}")
        print(f"FP16 output shape: {img_fp16.shape}")
        
        # Performance comparison
        print(f"\nPerformance Comparison:")
        print(f"  FP32 inference time: {fp32_stats['inference_time_ms']:.2f}ms")
        print(f"  FP16 inference time: {fp16_stats['inference_time_ms']:.2f}ms")
        
        speedup = fp32_stats['inference_time_ms'] / fp16_stats['inference_time_ms']
        print(f"  FP16 speedup: {speedup:.2f}x")
        
        # Quality comparison
        print(f"\nQuality Comparison:")
        print(f"  FP32 - Range: [{fp32_stats['min']:.4f}, {fp32_stats['max']:.4f}], Mean: {fp32_stats['mean']:.4f}, Std: {fp32_stats['std']:.4f}")
        print(f"  FP16 - Range: [{fp16_stats['min']:.4f}, {fp16_stats['max']:.4f}], Mean: {fp16_stats['mean']:.4f}, Std: {fp16_stats['std']:.4f}")
        
        # Statistical differences
        mean_diff = abs(fp32_stats['mean'] - fp16_stats['mean'])
        std_diff = abs(fp32_stats['std'] - fp16_stats['std'])
        range_diff = abs((fp32_stats['max'] - fp32_stats['min']) - (fp16_stats['max'] - fp16_stats['min']))
        
        print(f"\nStatistical Differences:")
        print(f"  Mean difference: {mean_diff:.6f}")
        print(f"  Std difference: {std_diff:.6f}")
        print(f"  Range difference: {range_diff:.6f}")
        
        # Pixel-level comparison
        if img_fp32.shape == img_fp16.shape:
            diff = np.abs(img_fp32.astype(np.float32) - img_fp16.astype(np.float32))
            max_pixel_diff = np.max(diff)
            mean_pixel_diff = np.mean(diff)
            std_pixel_diff = np.std(diff)
            
            # Calculate similarity metrics
            mse = np.mean((img_fp32.astype(np.float32) - img_fp16.astype(np.float32)) ** 2)
            psnr = 20 * np.log10(255.0 / np.sqrt(mse)) if mse > 0 else float('inf')
            
            # Structural similarity (simplified)
            correlation = np.corrcoef(img_fp32.flatten(), img_fp16.flatten())[0, 1]
            
            print(f"\nPixel-level Analysis:")
            print(f"  Max pixel difference: {max_pixel_diff:.2f} (out of 255)")
            print(f"  Mean pixel difference: {mean_pixel_diff:.2f}")
            print(f"  Std pixel difference: {std_pixel_diff:.2f}")
            print(f"  MSE: {mse:.4f}")
            print(f"  PSNR: {psnr:.2f} dB")
            print(f"  Correlation: {correlation:.6f}")
            
            # Create difference visualization
            diff_normalized = (diff / max_pixel_diff * 255).astype(np.uint8) if max_pixel_diff > 0 else np.zeros_like(diff, dtype=np.uint8)
            diff_img = Image.fromarray(diff_normalized)
            diff_img.save("difference_fp32_vs_fp16.png")
            print(f"✓ Difference visualization saved: difference_fp32_vs_fp16.png")
            
            # Quality assessment
            print(f"\nQuality Assessment:")
            if max_pixel_diff < 5:
                quality_rating = "EXCELLENT"
                quality_color = "✅"
            elif max_pixel_diff < 15:
                quality_rating = "VERY GOOD"
                quality_color = "✅"
            elif max_pixel_diff < 30:
                quality_rating = "GOOD"
                quality_color = "⚠️"
            elif max_pixel_diff < 50:
                quality_rating = "ACCEPTABLE"
                quality_color = "⚠️"
            else:
                quality_rating = "POOR"
                quality_color = "❌"
            
            print(f"  {quality_color} Overall quality: {quality_rating}")
            print(f"  {quality_color} Max difference: {max_pixel_diff:.1f}/255 ({max_pixel_diff/255*100:.1f}%)")
            
            # Performance vs Quality summary
            print(f"\nSummary:")
            print(f"  🚀 Performance gain: {speedup:.2f}x faster with FP16")
            print(f"  🎯 Quality retention: {quality_rating}")
            print(f"  📊 Correlation: {correlation:.4f} (1.0 = perfect match)")
            
            return max_pixel_diff < 50  # Accept if difference is reasonable
        else:
            print("❌ FAIL: Output shapes don't match")
            return False
            
    except Exception as e:
        print(f"❌ Comparison failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("Direct TensorRT FP16 Conversion - TensorRT 10.x Compatible")
    print("=" * 65)
    print("This script converts FP32 ONNX models directly to TensorRT FP16")
    print("and compares performance/quality with FP32 TensorRT engines.")
    print("Compatible with TensorRT 10.x and newer versions.")
    print("=" * 65)
    
    # Check TensorRT version
    print(f"TensorRT version: {trt.__version__}")
    
    # Check CUDA environment
    if not (CUDA_AVAILABLE or PYCUDA_AVAILABLE):
        print("\n❌ CUDA Environment Issue:")
        print("TensorRT requires CUDA for inference, but no CUDA support was detected.")
        print("\nPossible solutions:")
        print("1. Install PyTorch with CUDA support:")
        print("   conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia")
        print("2. Or install PyCUDA:")
        print("   pip install pycuda")
        print("3. Make sure CUDA drivers and toolkit are properly installed")
        return 1

    # Step PSB    
    # input_model = "InsPyReNet_1024x1024.onnx"
    # fp16_success, fp16_stats = test_tensorrt_engine("InsPyReNet_1024x1024.engine", "FP16")
    # if not fp16_success:
    #     print("❌ TensorRT FP16 engine test failed")

    
    # Input model
    input_model = "InsPyReNet_1024x1024.onnx"


    # Check if input exists
    if not os.path.exists(input_model):
        print(f"❌ Input model not found: {input_model}")
        print("Please make sure you have the FP32 ONNX model")
        return 1
    
    print(f"✅ Found input model: {input_model}")
    
    # Step 1: Test FP32 ONNX model
    print(f"\n{'='*60}")
    print("STEP 1: Test FP32 ONNX Model")
    print('='*60)
    
    if not test_fp32_onnx_model(input_model):
        print("❌ FP32 ONNX model has issues. Please fix before proceeding.")
        return 1
    
    # Step 2: Convert to TensorRT FP32 (for comparison baseline)
    print(f"\n{'='*60}")
    print("STEP 2: Convert to TensorRT FP32 (Baseline)")
    print('='*60)
    
    fp32_engine_path = convert_fp32_to_tensorrt_fp32(input_model)
    if not fp32_engine_path:
        print("❌ TensorRT FP32 conversion failed")
        return 1
    
    # Step 3: Convert to TensorRT FP16
    print(f"\n{'='*60}")
    print("STEP 3: Convert to TensorRT FP16")
    print('='*60)
    
    fp16_engine_path = convert_fp32_to_tensorrt_fp16(input_model)
    if not fp16_engine_path:
        print("❌ TensorRT FP16 conversion failed")
        return 1
    
    # Step 4: Test TensorRT FP32 engine
    print(f"\n{'='*60}")
    print("STEP 4: Test TensorRT FP32 Engine")
    print('='*60)
    
    fp32_success, fp32_stats = test_tensorrt_engine(fp32_engine_path, "FP32")
    if not fp32_success:
        print("❌ TensorRT FP32 engine test failed")
        return 1
    
    # Step 5: Test TensorRT FP16 engine
    print(f"\n{'='*60}")
    print("STEP 5: Test TensorRT FP16 Engine")
    print('='*60)
    
    fp16_success, fp16_stats = test_tensorrt_engine(fp16_engine_path, "FP16")
    if not fp16_success:
        print("❌ TensorRT FP16 engine test failed")
        return 1
    
    # Step 6: Advanced comparison
    print(f"\n{'='*60}")
    print("STEP 6: Advanced FP32 vs FP16 Comparison")
    print('='*60)
    
    comparison_success = compare_outputs_advanced(fp32_stats, fp16_stats)
    
    # Success summary
    print(f"\n{'🎉'*25}")
    print("SUCCESS! TENSORRT FP32 vs FP16 COMPARISON COMPLETE")
    print('🎉'*25)
    
    print(f"\nFiles created:")
    print(f"  ✅ {fp32_engine_path}")
    print(f"  ✅ {fp16_engine_path}")
    print(f"  ✅ test_output_fp32_reference.png (ONNX FP32 reference)")
    print(f"  ✅ test_output_tensorrt_fp32.png")
    print(f"  ✅ test_output_tensorrt_fp16.png")
    print(f"  ✅ difference_fp32_vs_fp16.png")
    
    print(f"\nPerformance Summary:")
    if fp32_stats and fp16_stats:
        speedup = fp32_stats['inference_time_ms'] / fp16_stats['inference_time_ms']
        print(f"  🚀 FP16 is {speedup:.2f}x faster than FP32")
        print(f"  ⏱️  FP32: {fp32_stats['inference_time_ms']:.2f}ms")
        print(f"  ⏱️  FP16: {fp16_stats['inference_time_ms']:.2f}ms")
    
    print(f"\nKey advantages of TensorRT 10.x approach:")
    print(f"  • Native TensorRT 10.x API compatibility")
    print(f"  • No ONNX FP16 type mismatch issues")
    print(f"  • Advanced mixed precision optimization")
    print(f"  • Detailed performance and quality analysis")
    print(f"  • Memory pool management for better efficiency")
    
    if comparison_success:
        print(f"  • ✅ FP16 quality is acceptable compared to FP32")
    else:
        print(f"  • ⚠️ FP16 quality differences detected - review comparison")
    
    print(f"\nUsage:")
    print(f"  FP32: python InferenceTensorRT.py --source input.png --engine {fp32_engine_path}")
    print(f"  FP16: python InferenceTensorRT.py --source input.png --engine {fp16_engine_path}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 